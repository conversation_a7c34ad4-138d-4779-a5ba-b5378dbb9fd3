import React, { useState } from 'react';
import { motion } from 'framer-motion';
import SEOHead from '../components/seo/SEOHead';
import { MapPinIcon, PhoneIcon, EnvelopeIcon, ClockIcon } from '@heroicons/react/24/outline';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    eventType: '',
    eventDate: '',
    guestCount: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        eventType: '',
        eventDate: '',
        guestCount: '',
        message: ''
      });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  return (
    <div className="min-h-screen">
      <SEOHead
        title="Contact Moon Event Center - Book Your Wedding or Event Today"
        description="Contact Moon Event Center in Richardson, Texas to book your wedding or special event. Get in touch for tours, pricing, and availability. Call (************* or fill out our contact form."
        keywords={[
          'contact Moon Event Center',
          'book wedding venue Richardson',
          'event venue contact',
          'wedding venue tours',
          'Richardson event center contact',
          'wedding venue pricing',
          'book event space Richardson'
        ]}
        url="https://mooneventcenter.com/contact"
        type="website"
      />

      {/* Hero Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-moon-navy mb-6">
              Contact Us
            </h1>
            <p className="text-xl md:text-2xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
              Ready to plan your perfect event? Let's bring your vision to life
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="section-padding">
        <div className="container-max">

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-serif font-bold text-moon-white mb-8">
                Get in Touch
              </h2>

              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-moon-gold rounded-full flex items-center justify-center flex-shrink-0">
                    <MapPinIcon className="w-6 h-6 text-moon-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-moon-white mb-2">Visit Our Venue</h3>
                    <p className="text-moon-white mb-2">
                      1801 N Plano Rd Ste 200<br />
                      Richardson, TX 75081
                    </p>
                    <a
                      href="https://maps.google.com/?q=1801+N+Plano+Rd+Ste+200,Richardson,TX+75081"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-moon-white hover:text-moon-gold transition-colors text-sm font-medium"
                    >
                      Get Directions →
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-moon-gold rounded-full flex items-center justify-center flex-shrink-0">
                    <PhoneIcon className="w-6 h-6 text-moon-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-moon-white mb-2">Call Us</h3>
                    <a
                      href="tel:+***********"
                      className="text-moon-white hover:text-moon-gold transition-colors text-lg"
                    >
                      (*************
                    </a>
                    <p className="text-sm text-moon-white mt-1">Available during business hours</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-moon-gold rounded-full flex items-center justify-center flex-shrink-0">
                    <EnvelopeIcon className="w-6 h-6 text-moon-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-moon-white mb-2">Email Us</h3>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-moon-white hover:text-moon-gold transition-colors"
                    >
                      <EMAIL>
                    </a>
                    <p className="text-sm text-moon-white mt-1">We'll respond within 24 hours</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-moon-gold rounded-full flex items-center justify-center flex-shrink-0">
                    <ClockIcon className="w-6 h-6 text-moon-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-moon-white mb-2">Business Hours</h3>
                    <div className="text-moon-white space-y-1">
                      <p>Open ⋅ Closes 2 AM</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-8 p-6 bg-moon-silver/10 rounded-lg">
                <h3 className="font-semibold text-moon-white mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <a
                    href="tel:+***********"
                    className="block w-full btn-primary text-center"
                  >
                    Schedule a Tour
                  </a>
                  <a
                    href="mailto:<EMAIL>?subject=Event Inquiry"
                    className="block w-full btn-primary text-center"
                  >
                    Request Quote
                  </a>
                </div>
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              className="bg-moon-white border border-moon-silver/20 rounded-lg p-8 shadow-lg"
            >
              <h2 className="text-3xl font-serif font-bold text-moon-navy mb-8">
                Send us a Message
              </h2>

              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800">Thank you! Your message has been sent successfully. We'll get back to you soon.</p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800">Sorry, there was an error sending your message. Please try again or call us directly.</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-black mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-black mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-black mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-black mb-2">
                      Phone
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="eventType" className="block text-sm font-medium text-black mb-2">
                      Event Type *
                    </label>
                    <select
                      id="eventType"
                      name="eventType"
                      value={formData.eventType}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    >
                      <option value="">Select event type</option>
                      <option value="wedding">Wedding</option>
                      <option value="corporate">Corporate Event</option>
                      <option value="birthday">Birthday Party</option>
                      <option value="anniversary">Anniversary</option>
                      <option value="quinceanera">Quinceañera</option>
                      <option value="graduation">Graduation</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="eventDate" className="block text-sm font-medium text-black mb-2">
                      Preferred Date
                    </label>
                    <input
                      type="date"
                      id="eventDate"
                      name="eventDate"
                      value={formData.eventDate}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    />
                  </div>
                  <div>
                    <label htmlFor="guestCount" className="block text-sm font-medium text-black mb-2">
                      Guest Count
                    </label>
                    <select
                      id="guestCount"
                      name="guestCount"
                      value={formData.guestCount}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    >
                      <option value="">Select guest count</option>
                      <option value="1-50">1-50 guests</option>
                      <option value="51-100">51-100 guests</option>
                      <option value="101-150">101-150 guests</option>
                      <option value="151-200">151-200 guests</option>
                      <option value="201-250">201-250 guests</option>
                      <option value="251-300">251-300 guests</option>
                      <option value="300+">300+ guests</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-black mb-2">
                    Tell us about your event *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={5}
                    required
                    className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-moon-gold/50 focus:border-moon-gold transition-colors text-black"
                    placeholder="Please share details about your event, including any specific requirements, preferred packages, or questions you may have..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full btn-primary ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>

                <p className="text-sm text-black text-center">
                  * Required fields. We'll respond within 24 hours.
                </p>
              </form>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
              Visit Our Venue
            </h2>
            <p className="text-xl text-moon-navy max-w-2xl mx-auto">
              Located in the heart of Richardson, Texas, our venue is easily accessible and offers ample parking
            </p>
          </motion.div>

          <motion.div
            variants={fadeInUp}
            className="bg-moon-white rounded-lg shadow-lg overflow-hidden"
          >
            {/* Google Maps Embed */}
            <div className="relative h-96">
              <iframe
                src="https://maps.google.com/maps?q=1801+N+Plano+Rd+Ste+200,Richardson,TX+75081&t=&z=15&ie=UTF8&iwloc=&output=embed"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Moon Event Center Location"
                className="absolute inset-0"
              ></iframe>
            </div>

            {/* Map Info */}
            <div className="p-6 bg-moon-white">
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-3">
                    <MapPinIcon className="w-6 h-6 text-moon-white" />
                  </div>
                  <h3 className="font-semibold text-moon-navy mb-2">Address</h3>
                  <p className="text-moon-navy text-sm">
                    1801 N Plano Rd Ste 200<br />
                    Richardson, TX 75081
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-moon-white font-bold">P</span>
                  </div>
                  <h3 className="font-semibold text-moon-navy mb-2">Parking</h3>
                  <p className="text-moon-navy text-sm">
                    Free parking available<br />
                    200+ spaces
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-moon-white font-bold">🚗</span>
                  </div>
                  <h3 className="font-semibold text-moon-navy mb-2">Access</h3>
                  <p className="text-moon-navy text-sm">
                    Easy highway access<br />
                    15 min from downtown Dallas
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section-padding">
        <div className="container-max">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
              Frequently Asked Questions
            </h2>
          </motion.div>

          <motion.div
            variants={fadeInUp}
            className="max-w-3xl mx-auto space-y-6"
          >
            {[
              {
                question: "How far in advance should I book?",
                answer: "We recommend booking 6-12 months in advance for weddings and 3-6 months for other events to ensure availability on your preferred date."
              },
              {
                question: "Do you provide catering services?",
                answer: "We work with preferred catering partners and can provide recommendations. You're also welcome to use your own caterer with our approval."
              },
              {
                question: "Is there a minimum guest count?",
                answer: "We don't have a strict minimum, but our packages are designed for events of 50+ guests. Contact us to discuss smaller gatherings."
              },
              {
                question: "Can I schedule a venue tour?",
                answer: "Absolutely! We offer complimentary venue tours by appointment. Call us or fill out the contact form to schedule your visit."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="font-semibold text-moon-navy mb-3">{faq.question}</h3>
                <p className="text-moon-navy">{faq.answer}</p>
              </div>
            ))}
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
