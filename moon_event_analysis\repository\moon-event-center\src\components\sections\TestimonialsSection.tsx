import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';

interface Testimonial {
  id: number;
  name: string;
  role: string;
  company?: string;
  eventType: string;
  rating: number;
  content: string;
  image?: string;
  eventDate: string;
  location?: string;
}

interface TestimonialsSectionProps {
  title?: string;
  subtitle?: string;
  showControls?: boolean;
  autoPlay?: boolean;
  displayMode?: 'carousel' | 'grid';
  maxItems?: number;
  className?: string;
}

const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
  title = "What Our Clients Say",
  subtitle = "Real experiences from real celebrations",
  showControls = true,
  autoPlay = true,
  displayMode = 'carousel',
  maxItems = 6,
  className = ""
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);

  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Bride & Groom",
      eventType: "Wedding",
      rating: 5,
      content: "Moon Event Center made our wedding day absolutely magical! The staff was incredibly professional, the venue was stunning, and every detail was perfect. Our guests are still talking about how beautiful everything was. We couldn't have asked for a better experience.",
      eventDate: "October 2023",
      location: "Richardson, TX"
    },
    {
      id: 2,
      name: "Jennifer Martinez",
      role: "Event Coordinator",
      company: "TechCorp Solutions",
      eventType: "Corporate Gala",
      rating: 5,
      content: "We've hosted our annual company gala at Moon Event Center for two years running, and they never disappoint. The team is professional, the facilities are top-notch, and they handle every detail seamlessly. Our employees and clients always have an amazing time.",
      eventDate: "December 2023",
      location: "Richardson, TX"
    },
    {
      id: 3,
      name: "Maria Rodriguez",
      role: "Mother of Quinceañera",
      eventType: "Quinceañera",
      rating: 5,
      content: "My daughter's quinceañera was everything we dreamed of and more! The Moon Event Center team understood our cultural traditions and helped us create a celebration that honored our heritage while being absolutely beautiful. The attention to detail was incredible.",
      eventDate: "September 2023",
      location: "Richardson, TX"
    },
    {
      id: 4,
      name: "Robert & Linda Chen",
      role: "Anniversary Couple",
      eventType: "50th Anniversary",
      rating: 5,
      content: "Celebrating our 50th anniversary at Moon Event Center was perfect. The elegant atmosphere, excellent service, and beautiful setting made our milestone celebration truly special. Our family and friends had a wonderful time, and the memories will last forever.",
      eventDate: "November 2023",
      location: "Richardson, TX"
    },
    {
      id: 5,
      name: "David Thompson",
      role: "Birthday Celebrant",
      eventType: "60th Birthday Party",
      rating: 5,
      content: "My wife surprised me with a 60th birthday party at Moon Event Center, and it was incredible! The venue was elegant, the staff was attentive, and everything ran smoothly. It was the perfect way to celebrate with family and friends.",
      eventDate: "August 2023",
      location: "Richardson, TX"
    },
    {
      id: 6,
      name: "Amanda Foster",
      role: "Event Planner",
      company: "Elite Events Dallas",
      eventType: "Corporate Conference",
      rating: 5,
      content: "As a professional event planner, I've worked with many venues, and Moon Event Center consistently exceeds expectations. Their team is responsive, professional, and truly cares about making each event successful. I highly recommend them to all my clients.",
      eventDate: "January 2024",
      location: "Richardson, TX"
    }
  ];

  const displayedTestimonials = testimonials.slice(0, maxItems);

  useEffect(() => {
    if (isAutoPlaying && displayMode === 'carousel') {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % displayedTestimonials.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlaying, displayedTestimonials.length, displayMode]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayedTestimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayedTestimonials.length) % displayedTestimonials.length);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
  };

  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`w-5 h-5 ${i < rating ? 'text-moon-gold' : 'text-moon-silver/30'}`}
        aria-label={i < rating ? 'Filled star' : 'Empty star'}
        role="img"
      />
    ));
  };

  const TestimonialCard: React.FC<{ testimonial: Testimonial; index?: number }> = ({ testimonial, index }) => (
    <motion.div
      variants={fadeInUp}
      className="bg-moon-white p-8 rounded-lg shadow-lg h-full flex flex-col"
    >
      <div className="flex items-center mb-4">
        {renderStars(testimonial.rating)}
      </div>
      
      <blockquote className="text-moon-navy mb-6 flex-grow">
        "{testimonial.content}"
      </blockquote>

      <div className="border-t border-moon-silver/20 pt-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-semibold text-moon-navy">{testimonial.name}</h4>
            <p className="text-sm text-moon-navy">
              {testimonial.role}
              {testimonial.company && ` • ${testimonial.company}`}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium text-moon-gold">{testimonial.eventType}</p>
            <p className="text-xs text-moon-navy">{testimonial.eventDate}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <section className={`section-padding ${className}`} aria-labelledby="testimonials-heading">
      <div className="container-max">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            id="testimonials-heading"
            variants={fadeInUp}
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-moon-white mb-6"
          >
            {title}
          </motion.h2>
          <motion.p 
            variants={fadeInUp}
            className="text-xl text-moon-white max-w-3xl mx-auto leading-relaxed"
          >
            {subtitle}
          </motion.p>
        </motion.div>

        {displayMode === 'carousel' ? (
          <div className="relative" role="region" aria-label="Testimonials carousel">
            <div
              className="overflow-hidden"
              onMouseEnter={() => setIsAutoPlaying(false)}
              onMouseLeave={() => setIsAutoPlaying(autoPlay)}
              aria-live="polite"
              aria-atomic="true"
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <TestimonialCard testimonial={displayedTestimonials[currentIndex]} />
                </motion.div>
              </AnimatePresence>
            </div>

            {showControls && (
              <>
                <button
                  onClick={prevTestimonial}
                  className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 w-12 h-12 bg-moon-white shadow-lg rounded-full flex items-center justify-center text-moon-navy hover:bg-moon-gold hover:text-moon-white transition-colors"
                  aria-label="Previous testimonial"
                >
                  <ChevronLeftIcon className="w-6 h-6" aria-label="Previous arrow" role="img" />
                </button>

                <button
                  onClick={nextTestimonial}
                  className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 w-12 h-12 bg-moon-white shadow-lg rounded-full flex items-center justify-center text-moon-navy hover:bg-moon-gold hover:text-moon-white transition-colors"
                  aria-label="Next testimonial"
                >
                  <ChevronRightIcon className="w-6 h-6" aria-label="Next arrow" role="img" />
                </button>
              </>
            )}

            <div className="flex justify-center mt-8 space-x-2">
              {displayedTestimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-moon-gold' : 'bg-moon-silver/30'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>
        ) : (
          <motion.div
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {displayedTestimonials.map((testimonial, index) => (
              <TestimonialCard key={testimonial.id} testimonial={testimonial} index={index} />
            ))}
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default TestimonialsSection;
