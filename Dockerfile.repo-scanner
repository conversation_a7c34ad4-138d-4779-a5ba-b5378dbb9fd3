# Dockerfile for secure external repository testing
# This container provides an isolated environment for cloning and analyzing external repositories
FROM python:3.11-slim

# Install system dependencies for security tools
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    build-essential \
    libssl-dev \
    libffi-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    nmap \
    netcat-traditional \
    dnsutils \
    iputils-ping \
    net-tools \
    procps \
    vim \
    nano \
    jq \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Install Java for ZAP (if needed)
RUN apt-get update && apt-get install -y default-jdk && rm -rf /var/lib/apt/lists/*

# Install Trivy for dependency scanning
RUN wget -qO- https://aquasecurity.github.io/trivy-repo/deb/public.key | apt-key add - && \
    echo "deb https://aquasecurity.github.io/trivy-repo/deb generic main" | tee -a /etc/apt/sources.list.d/trivy.list && \
    apt-get update && \
    apt-get install -y trivy && \
    rm -rf /var/lib/apt/lists/*

# Install Semgrep
RUN python -m pip install --upgrade pip && \
    pip install semgrep

# Set up working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy security tools
COPY config.yml .
COPY run.py .
COPY fuzz.py .
COPY verify_dast.py .

# Create directories for testing
RUN mkdir -p /app/test-repos /app/results /app/temp

# Set environment variables
ENV PYTHONPATH=/app
ENV REPO_TEST_DIR=/app/test-repos
ENV RESULTS_DIR=/app/results

# Create non-root user for security
RUN useradd -m -u 1000 security && \
    chown -R security:security /app
USER security

# Create a script for safe repository testing
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
REPO_URL="$1"\n\
REPO_NAME="$2"\n\
\n\
if [ -z "$REPO_URL" ] || [ -z "$REPO_NAME" ]; then\n\
    echo "Usage: $0 <repo_url> <repo_name>"\n\
    exit 1\n\
fi\n\
\n\
echo "🔒 Starting secure repository analysis..."\n\
echo "📂 Repository: $REPO_URL"\n\
echo "📁 Local name: $REPO_NAME"\n\
\n\
# Clone repository into isolated directory\n\
cd /app/test-repos\n\
echo "📥 Cloning repository..."\n\
git clone "$REPO_URL" "$REPO_NAME" || exit 1\n\
\n\
# Change to repository directory\n\
cd "$REPO_NAME"\n\
\n\
echo "🔍 Running security analysis..."\n\
\n\
# Run static analysis on the cloned repository\n\
python /app/run.py --static-only --results "/app/results/${REPO_NAME}_security_scan" --target-dir "$(pwd)"\n\
\n\
echo "✅ Security analysis completed!"\n\
echo "📊 Results saved to: /app/results/${REPO_NAME}_security_scan"\n\
' > /app/scan_repo.sh && chmod +x /app/scan_repo.sh

# Default command shows help
CMD ["echo", "🔒 Secure Repository Scanner Ready! Use: docker run -v $(pwd)/results:/app/results repo-scanner ./scan_repo.sh <repo_url> <repo_name>"]
