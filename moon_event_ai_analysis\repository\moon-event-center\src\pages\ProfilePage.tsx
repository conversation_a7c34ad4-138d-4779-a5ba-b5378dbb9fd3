import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  UserIcon, 
  EnvelopeIcon, 
  PhoneIcon, 
  CalendarIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { useBooking } from '../contexts/BookingContext';
import DashboardCard from '../components/dashboard/DashboardCard';
import StatsCard from '../components/dashboard/StatsCard';
import Button from '../components/ui/Button';
import SEOHead from '../components/seo/SEOHead';

const ProfilePage: React.FC = () => {
  const { user, updateProfile, isLoading } = useAuth();
  const { getBookingStats } = useBooking();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || ''
  });
  const [updateLoading, setUpdateLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const stats = user ? getBookingStats(user.id) : null;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    if (!user) return;

    setUpdateLoading(true);
    setMessage(null);

    try {
      const result = await updateProfile(formData);
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Profile updated successfully!' });
        setIsEditing(false);
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to update profile' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' });
    } finally {
      setUpdateLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || ''
    });
    setIsEditing(false);
    setMessage(null);
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-moon-navy mb-4">Access Denied</h2>
          <p className="text-moon-gray">Please log in to view your profile.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-moon-navy/5 to-moon-silver/10 py-8">
      <SEOHead
        title="My Profile - Moon Event Center"
        description="Manage your Moon Event Center profile and account settings."
        keywords={['profile', 'account', 'settings', 'Moon Event Center']}
        url="https://mooneventcenter.com/profile"
        noIndex={true}
      />

      <div className="container-max">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-serif font-bold text-moon-navy mb-2">My Profile</h1>
            <p className="text-moon-gray">Manage your account information and preferences</p>
          </div>

          {/* Stats Cards */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <StatsCard
                title="Total Bookings"
                value={stats.total}
                icon={CalendarIcon}
                color="blue"
              />
              <StatsCard
                title="Confirmed Events"
                value={stats.confirmed}
                icon={CheckIcon}
                color="green"
              />
              <StatsCard
                title="Total Spent"
                value={`$${stats.totalRevenue.toLocaleString()}`}
                subtitle="Lifetime value"
                color="purple"
              />
              <StatsCard
                title="Member Since"
                value={new Date(user.createdAt).getFullYear()}
                subtitle={new Date(user.createdAt).toLocaleDateString()}
                color="yellow"
              />
            </div>
          )}

          {/* Profile Information */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Details */}
            <div className="lg:col-span-2">
              <DashboardCard
                title="Profile Information"
                loading={isLoading}
                headerAction={
                  !isEditing ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditing(true)}
                      className="flex items-center space-x-2"
                    >
                      <PencilIcon className="w-4 h-4" />
                      <span>Edit</span>
                    </Button>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancel}
                        className="flex items-center space-x-2"
                      >
                        <XMarkIcon className="w-4 h-4" />
                        <span>Cancel</span>
                      </Button>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={handleSave}
                        disabled={updateLoading}
                        className="flex items-center space-x-2"
                      >
                        <CheckIcon className="w-4 h-4" />
                        <span>{updateLoading ? 'Saving...' : 'Save'}</span>
                      </Button>
                    </div>
                  )
                }
              >
                {message && (
                  <div className={`mb-6 p-4 rounded-lg ${
                    message.type === 'success' 
                      ? 'bg-green-50 text-green-800 border border-green-200' 
                      : 'bg-red-50 text-red-800 border border-red-200'
                  }`}>
                    {message.text}
                  </div>
                )}

                <div className="space-y-6">
                  {/* First Name */}
                  <div>
                    <label className="block text-sm font-medium text-moon-gray mb-2">
                      First Name
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                        placeholder="Enter your first name"
                      />
                    ) : (
                      <div className="flex items-center space-x-3">
                        <UserIcon className="w-5 h-5 text-moon-gray" />
                        <span className="text-moon-navy">{user.firstName}</span>
                      </div>
                    )}
                  </div>

                  {/* Last Name */}
                  <div>
                    <label className="block text-sm font-medium text-moon-gray mb-2">
                      Last Name
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                        placeholder="Enter your last name"
                      />
                    ) : (
                      <div className="flex items-center space-x-3">
                        <UserIcon className="w-5 h-5 text-moon-gray" />
                        <span className="text-moon-navy">{user.lastName}</span>
                      </div>
                    )}
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-sm font-medium text-moon-gray mb-2">
                      Email Address
                    </label>
                    {isEditing ? (
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                        placeholder="Enter your email address"
                      />
                    ) : (
                      <div className="flex items-center space-x-3">
                        <EnvelopeIcon className="w-5 h-5 text-moon-gray" />
                        <span className="text-moon-navy">{user.email}</span>
                      </div>
                    )}
                  </div>

                  {/* Phone */}
                  <div>
                    <label className="block text-sm font-medium text-moon-gray mb-2">
                      Phone Number
                    </label>
                    {isEditing ? (
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                        placeholder="Enter your phone number"
                      />
                    ) : (
                      <div className="flex items-center space-x-3">
                        <PhoneIcon className="w-5 h-5 text-moon-gray" />
                        <span className="text-moon-navy">{user.phone || 'Not provided'}</span>
                      </div>
                    )}
                  </div>
                </div>
              </DashboardCard>
            </div>

            {/* Account Summary */}
            <div>
              <DashboardCard title="Account Summary">
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b border-moon-silver/20">
                    <span className="text-moon-gray">Account Type</span>
                    <span className="text-moon-navy font-medium capitalize">{user.role}</span>
                  </div>
                  
                  <div className="flex justify-between items-center py-2 border-b border-moon-silver/20">
                    <span className="text-moon-gray">Member Since</span>
                    <span className="text-moon-navy font-medium">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  
                  {user.lastLogin && (
                    <div className="flex justify-between items-center py-2 border-b border-moon-silver/20">
                      <span className="text-moon-gray">Last Login</span>
                      <span className="text-moon-navy font-medium">
                        {new Date(user.lastLogin).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center py-2">
                    <span className="text-moon-gray">Account Status</span>
                    <span className="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Active
                    </span>
                  </div>
                </div>
              </DashboardCard>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ProfilePage;
