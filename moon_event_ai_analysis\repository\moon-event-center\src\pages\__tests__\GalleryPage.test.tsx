import { describe, it, expect } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render } from '../../test/utils/test-utils'
import GalleryPage from '../GalleryPage'

describe('GalleryPage Component', () => {
  it('renders gallery page with hero section', () => {
    render(<GalleryPage />)
    
    // Check main heading
    expect(screen.getByRole('heading', { name: 'Event Gallery' })).toBeInTheDocument()
    
    // Check description
    expect(screen.getByText(/explore the magic of moon event center/i)).toBeInTheDocument()
  })

  it('renders category filter buttons', () => {
    render(<GalleryPage />)
    
    // Check for category buttons
    expect(screen.getByRole('button', { name: 'All Events' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Weddings' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Corporate' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Social Events' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Venue Features' })).toBeInTheDocument()
  })

  it('filters images by category', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    // Initially should show all images
    expect(screen.getAllByRole('img')).toHaveLength(7)
    
    // Filter by weddings
    const weddingsButton = screen.getByRole('button', { name: 'Weddings' })
    await user.click(weddingsButton)
    
    // Should show only wedding images (2 wedding images)
    const weddingImages = screen.getAllByRole('img')
    expect(weddingImages.length).toBe(2)
    
    // Check that wedding category button is active
    expect(weddingsButton).toHaveClass('bg-moon-navy')
  })

  it('displays gallery images with proper attributes', () => {
    render(<GalleryPage />)
    
    // Check for specific images
    expect(screen.getByAltText('Wedding Celebration Dancefloor')).toBeInTheDocument()
    expect(screen.getByAltText('Game Day Social Event')).toBeInTheDocument()
    expect(screen.getByAltText('Wedding Reception Setup')).toBeInTheDocument()
    expect(screen.getByAltText('Moon Event Center Venue')).toBeInTheDocument()
    
    // Check that images have proper src attributes
    const images = screen.getAllByRole('img')
    images.forEach(img => {
      expect(img).toHaveAttribute('src')
      expect(img).toHaveAttribute('alt')
    })
  })

  it('opens lightbox when image is clicked', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    // Click on first image
    const firstImage = screen.getByAltText('Wedding Celebration Dancefloor')
    await user.click(firstImage)
    
    // Check that lightbox opens
    await waitFor(() => {
      expect(screen.getAllByText('Elegant Wedding Reception')).toHaveLength(2) // One in grid, one in lightbox
      expect(screen.getByText(/beautiful wedding celebration with our signature lighting/i)).toBeInTheDocument()
    })
  })

  it('closes lightbox when close button is clicked', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    // Open lightbox
    const firstImage = screen.getByAltText('Wedding Celebration Dancefloor')
    await user.click(firstImage)
    
    // Wait for lightbox to open - check for lightbox-specific content
    await waitFor(() => {
      expect(screen.getAllByText('Elegant Wedding Reception')).toHaveLength(2) // One in grid, one in lightbox
    })
    
    // Click close button
    const closeButton = screen.getByRole('button', { name: '✕' })
    await user.click(closeButton)
    
    // Check that lightbox closes - should only have one instance (in grid)
    await waitFor(() => {
      expect(screen.getAllByText('Elegant Wedding Reception')).toHaveLength(1)
    })
  })

  it('closes lightbox when clicking outside modal', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    // Open lightbox
    const firstImage = screen.getByAltText('Wedding Celebration Dancefloor')
    await user.click(firstImage)
    
    // Wait for lightbox to open
    await waitFor(() => {
      expect(screen.getAllByText('Elegant Wedding Reception')).toHaveLength(2) // One in grid, one in lightbox
    })
    
    // Click on backdrop (outside modal)
    const lightboxTitles = screen.getAllByText('Elegant Wedding Reception')
    const backdrop = lightboxTitles[1]?.closest('[class*="fixed"]') // Get the lightbox version
    if (backdrop) {
      await user.click(backdrop)

      // Check that lightbox closes - should only have one instance (in grid)
      await waitFor(() => {
        expect(screen.getAllByText('Elegant Wedding Reception')).toHaveLength(1)
      })
    }
  })

  it('shows hover effects on gallery images', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    const firstImageContainer = screen.getByAltText('Wedding Celebration Dancefloor').closest('.group')
    expect(firstImageContainer).toBeInTheDocument()
    
    // Check for hover text - multiple elements have this text
    if (firstImageContainer) {
      await user.hover(firstImageContainer)
      expect(screen.getAllByText('Click to view details').length).toBeGreaterThan(0)
    }
  })

  it('has proper accessibility attributes', () => {
    render(<GalleryPage />)
    
    // Check that images have alt text
    const images = screen.getAllByRole('img')
    images.forEach(img => {
      expect(img).toHaveAttribute('alt')
      expect(img.getAttribute('alt')).not.toBe('')
    })
    
    // Check that buttons are properly labeled
    const categoryButtons = screen.getAllByRole('button')
    categoryButtons.forEach(button => {
      expect(button).toHaveTextContent(/\w+/)
    })
  })

  it('maintains category selection state', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    // Select corporate category
    const corporateButton = screen.getByRole('button', { name: 'Corporate' })
    await user.click(corporateButton)
    
    // Check that corporate button is active
    expect(corporateButton).toHaveClass('bg-moon-navy')
    
    // Check that other buttons are not active
    const allEventsButton = screen.getByRole('button', { name: 'All Events' })
    expect(allEventsButton).not.toHaveClass('bg-moon-navy')
  })

  it('displays correct image titles and descriptions in lightbox', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    // Test different images
    const socialImage = screen.getByAltText('Game Day Social Event')
    await user.click(socialImage)

    await waitFor(() => {
      expect(screen.getAllByText('Game Day Celebration')).toHaveLength(2) // One in grid, one in lightbox
      expect(screen.getByText(/fun social game day event with friends/i)).toBeInTheDocument()
    })

    // Close and test another image
    const closeButton = screen.getByRole('button', { name: '✕' })
    await user.click(closeButton)

    await waitFor(() => {
      expect(screen.getAllByText('Game Day Celebration')).toHaveLength(1) // Only in grid after lightbox closes
    })
  })

  it('handles keyboard navigation for accessibility', async () => {
    const user = userEvent.setup()
    render(<GalleryPage />)
    
    // Tab to first category button
    await user.tab()
    expect(screen.getByRole('button', { name: 'All Events' })).toHaveFocus()
    
    // Tab through category buttons
    await user.tab()
    expect(screen.getByRole('button', { name: 'Weddings' })).toHaveFocus()
  })
})
