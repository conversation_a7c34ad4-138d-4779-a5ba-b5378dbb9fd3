import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BellIcon, 
  ShieldCheckIcon, 
  ChatBubbleLeftRightIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { useBooking } from '../contexts/BookingContext';
import type { UserPreferences } from '../types/booking';
import DashboardCard from '../components/dashboard/DashboardCard';
import Button from '../components/ui/Button';
import SEOHead from '../components/seo/SEOHead';

const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const { userPreferences, updateUserPreferences, isLoading } = useBooking();
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    if (userPreferences) {
      setPreferences(userPreferences);
    }
  }, [userPreferences]);

  if (!user || !preferences) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-moon-gold mx-auto mb-4"></div>
          <p className="text-moon-gray">Loading settings...</p>
        </div>
      </div>
    );
  }

  const handlePreferenceChange = (section: keyof UserPreferences, key: string, value: any) => {
    setPreferences(prev => {
      if (!prev) return null;
      return {
        ...prev,
        [section]: {
          ...prev[section],
          [key]: value
        }
      };
    });
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!preferences) return;

    setSaveLoading(true);
    setMessage(null);

    try {
      const result = await updateUserPreferences(preferences);
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Settings saved successfully!' });
        setHasChanges(false);
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to save settings' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' });
    } finally {
      setSaveLoading(false);
    }
  };

  const handleReset = () => {
    if (userPreferences) {
      setPreferences(userPreferences);
      setHasChanges(false);
      setMessage(null);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-moon-navy/5 to-moon-silver/10 py-8">
      <SEOHead
        title="Settings - Moon Event Center"
        description="Manage your account settings and preferences at Moon Event Center."
        keywords={['settings', 'preferences', 'account', 'Moon Event Center']}
        url="https://mooneventcenter.com/settings"
        noIndex={true}
      />

      <div className="container-max">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-serif font-bold text-moon-navy mb-2">Settings</h1>
              <p className="text-moon-gray">Manage your account preferences and privacy settings</p>
            </div>
            
            {hasChanges && (
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="flex items-center space-x-2"
                >
                  <XMarkIcon className="w-4 h-4" />
                  <span>Reset</span>
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSave}
                  disabled={saveLoading}
                  className="flex items-center space-x-2"
                >
                  <CheckIcon className="w-4 h-4" />
                  <span>{saveLoading ? 'Saving...' : 'Save Changes'}</span>
                </Button>
              </div>
            )}
          </div>

          {message && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`mb-6 p-4 rounded-lg ${
                message.type === 'success' 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}
            >
              {message.text}
            </motion.div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Notification Settings */}
            <DashboardCard
              title="Notification Preferences"
              headerAction={<BellIcon className="w-5 h-5 text-moon-gold" />}
              loading={isLoading}
            >
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-moon-navy">Email Notifications</h4>
                    <p className="text-sm text-moon-gray">Receive notifications via email</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.email}
                      onChange={(e) => handlePreferenceChange('notifications', 'email', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-moon-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-moon-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-moon-navy">SMS Notifications</h4>
                    <p className="text-sm text-moon-gray">Receive notifications via text message</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.sms}
                      onChange={(e) => handlePreferenceChange('notifications', 'sms', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-moon-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-moon-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-moon-navy">Booking Reminders</h4>
                    <p className="text-sm text-moon-gray">Get reminders about upcoming events</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.bookingReminders}
                      onChange={(e) => handlePreferenceChange('notifications', 'bookingReminders', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-moon-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-moon-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-moon-navy">Promotional Emails</h4>
                    <p className="text-sm text-moon-gray">Receive special offers and promotions</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.promotionalEmails}
                      onChange={(e) => handlePreferenceChange('notifications', 'promotionalEmails', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-moon-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-moon-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-moon-navy">Event Updates</h4>
                    <p className="text-sm text-moon-gray">Get updates about your booked events</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.eventUpdates}
                      onChange={(e) => handlePreferenceChange('notifications', 'eventUpdates', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-moon-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-moon-gold"></div>
                  </label>
                </div>
              </div>
            </DashboardCard>

            {/* Privacy Settings */}
            <DashboardCard
              title="Privacy Settings"
              headerAction={<ShieldCheckIcon className="w-5 h-5 text-moon-gold" />}
              loading={isLoading}
            >
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-moon-gray mb-2">
                    Profile Visibility
                  </label>
                  <select
                    value={preferences.privacy.profileVisibility}
                    onChange={(e) => handlePreferenceChange('privacy', 'profileVisibility', e.target.value)}
                    className="w-full px-3 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                  >
                    <option value="private">Private</option>
                    <option value="public">Public</option>
                  </select>
                  <p className="text-xs text-moon-gray mt-1">
                    Control who can see your profile information
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-moon-navy">Share Contact Information</h4>
                    <p className="text-sm text-moon-gray">Allow staff to share your contact info with vendors</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.privacy.shareContactInfo}
                      onChange={(e) => handlePreferenceChange('privacy', 'shareContactInfo', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-moon-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-moon-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-moon-navy">Marketing Communications</h4>
                    <p className="text-sm text-moon-gray">Allow us to send you marketing materials</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.privacy.allowMarketing}
                      onChange={(e) => handlePreferenceChange('privacy', 'allowMarketing', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-moon-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-moon-gold"></div>
                  </label>
                </div>
              </div>
            </DashboardCard>

            {/* Communication Preferences */}
            <DashboardCard
              title="Communication Preferences"
              headerAction={<ChatBubbleLeftRightIcon className="w-5 h-5 text-moon-gold" />}
              loading={isLoading}
            >
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-moon-gray mb-2">
                    Preferred Contact Method
                  </label>
                  <select
                    value={preferences.communication.preferredContactMethod}
                    onChange={(e) => handlePreferenceChange('communication', 'preferredContactMethod', e.target.value)}
                    className="w-full px-3 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                  >
                    <option value="email">Email</option>
                    <option value="phone">Phone</option>
                    <option value="sms">SMS</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-moon-gray mb-2">
                    Timezone
                  </label>
                  <select
                    value={preferences.communication.timezone}
                    onChange={(e) => handlePreferenceChange('communication', 'timezone', e.target.value)}
                    className="w-full px-3 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                  >
                    <option value="America/Chicago">Central Time (CT)</option>
                    <option value="America/New_York">Eastern Time (ET)</option>
                    <option value="America/Denver">Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-moon-gray mb-2">
                    Language
                  </label>
                  <select
                    value={preferences.communication.language}
                    onChange={(e) => handlePreferenceChange('communication', 'language', e.target.value)}
                    className="w-full px-3 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                  >
                    <option value="en">English</option>
                    <option value="es">Español</option>
                  </select>
                </div>
              </div>
            </DashboardCard>

            {/* Account Actions */}
            <DashboardCard title="Account Actions" loading={isLoading}>
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h4 className="font-medium text-yellow-800 mb-2">Change Password</h4>
                  <p className="text-sm text-yellow-700 mb-3">
                    Update your password to keep your account secure.
                  </p>
                  <Button variant="outline" size="sm">
                    Change Password
                  </Button>
                </div>

                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-2">Delete Account</h4>
                  <p className="text-sm text-red-700 mb-3">
                    Permanently delete your account and all associated data. This action cannot be undone.
                  </p>
                  <Button variant="outline" size="sm" className="text-red-600 border-red-300 hover:bg-red-50">
                    Delete Account
                  </Button>
                </div>
              </div>
            </DashboardCard>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default SettingsPage;
