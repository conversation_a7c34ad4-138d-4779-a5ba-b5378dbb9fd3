import React from 'react';
import { motion } from 'framer-motion';
import SEOHead from '../components/seo/SEOHead';

const AboutPage: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  return (
    <div className="min-h-screen">
      <SEOHead
        title="About Moon Event Center - Premier Wedding Venue in Richardson, Texas"
        description="Learn about Moon Event Center's story, mission, and commitment to creating magical wedding and event experiences in Richardson, Texas. Discover our elegant venue and exceptional service."
        keywords={[
          'about Moon Event Center',
          'wedding venue Richardson TX',
          'event center story',
          'Richardson wedding venue',
          'Texas event space',
          'wedding venue history',
          'event planning Richardson'
        ]}
        url="https://mooneventcenter.com/about"
        type="website"
      />

      {/* Hero Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-moon-navy mb-6">
              About Moon Event Center
            </h1>
            <p className="text-xl md:text-2xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
              "Life Is an event. Make it memorable."
            </p>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid lg:grid-cols-2 gap-12 items-center"
          >
            <motion.div variants={fadeInUp}>
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
                Our Story
              </h2>
              <div className="space-y-6 text-lg text-moon-text leading-relaxed">
                <p>
                  After searching high and low for the perfect venue to host our own office parties — and coming up short every time — we decided to build the space ourselves. Not just to meet our own needs, but to create a place that others could make their own, no matter their style or vision.
                </p>
                <p>
                  Too many venues had plenty of floor space but only one or two bathrooms. Some had audio systems, but nothing a live band would be happy with. Others offered TVs, but none big or clear enough to present to 300 people from 70 feet away. We found catering setups that lacked the power for electric warmers, and no onsite fridges to keep Bee.. ahem — beverages cold.
                </p>
                <p>
                  These were just a few of the issues we knew we had to solve from the very beginning. But don't get us wrong — we also know every event is different, every culture brings its own flair, and every celebration has its own needs. That's why we're always open to feedback and would love to assist you with YOUR special event!
                </p>
                <p>
                  If there's something you need for your special event, just ask — we're up for the challenge!
                </p>
              </div>
            </motion.div>
            <motion.div variants={fadeInUp} className="relative">
              <div className="aspect-square rounded-lg overflow-hidden shadow-xl">
                <img
                  src="/assets/Images/Wedding Celebration Dancefloor.png"
                  alt="Moon Event Center Interior"
                  className="w-full h-full object-cover"
                />
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Mission & Values Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6"
            >
              Our Mission & Values
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-moon-navy max-w-3xl mx-auto leading-relaxed"
            >
              To Help YOU Make it Memorable!
            </motion.p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-3 gap-8"
          >
            <motion.div variants={fadeInUp} className="text-center p-6">
              <div className="w-16 h-16 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-moon-white">✨</span>
              </div>
              <h3 className="text-xl font-semibold text-moon-navy mb-3">Excellence</h3>
              <p className="text-moon-navy">
                We strive for perfection in every detail, ensuring your event exceeds expectations.
              </p>
            </motion.div>

            <motion.div variants={fadeInUp} className="text-center p-6">
              <div className="w-16 h-16 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-moon-white">🌙</span>
              </div>
              <h3 className="text-xl font-semibold text-moon-navy mb-3">Elegance</h3>
              <p className="text-moon-navy">
                Our sophisticated atmosphere creates the perfect backdrop for memorable celebrations.
              </p>
            </motion.div>

            <motion.div variants={fadeInUp} className="text-center p-6">
              <div className="w-16 h-16 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-moon-white">💫</span>
              </div>
              <h3 className="text-xl font-semibold text-moon-navy mb-3">Experience</h3>
              <p className="text-moon-navy">
                We create unforgettable moments that will be treasured for a lifetime.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Venue Highlights Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            <motion.div variants={fadeInUp} className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
                Venue Highlights
              </h2>
              <p className="text-xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
                Discover what makes Moon Event Center the perfect choice for your special occasion
              </p>
            </motion.div>

            <motion.div
              variants={staggerContainer}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Elegant Ballroom</h3>
                <p className="text-moon-navy mb-4">
                  Our grand ballroom features soaring ceilings, crystal chandeliers, and space for up to 300 guests.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Professional lighting system</li>
                  <li>• 4 Gigabit Guest Wifi</li>
                  <li>• Dance floor</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Audio & Video</h3>
                <p className="text-moon-navy mb-4">
                  A forty foot wall FULLY dedicated as an HD Television paired with High End Audio for Music, Presentations, Karaoko or A Live Sound Band / DJ.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Superbowl! & All Other Television Events!</li>
                  <li>• Private Movie theater or Game Room</li>
                  <li>• Next Level Powerpoint Presentations</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Bridal Suite</h3>
                <p className="text-moon-navy mb-4">
                  Luxurious preparation space with all the amenities for the perfect getting-ready experience.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Private bathroom & Shower</li>
                  <li>• Professional lighting</li>
                  <li>• Comfortable seating area</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Catering Kitchen</h3>
                <p className="text-moon-navy mb-4">
                  State-of-the-art commercial kitchen for seamless food service and preparation.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Professional equipment</li>
                  <li>• Ample prep space</li>
                  <li>• Service areas</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Parking & Access</h3>
                <p className="text-moon-navy mb-4">
                  Convenient location with ample parking and easy access for all guests.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• 250+ parking spaces</li>
                  <li>• ADA compliant</li>
                  <li>• Valet service available</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Event Coordination</h3>
                <p className="text-moon-navy mb-4">
                  Professional event coordination to ensure every detail is perfectly executed.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Dedicated coordinator</li>
                  <li>• Timeline management</li>
                  <li>• Vendor coordination</li>
                </ul>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
