import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  HeartIcon, 
  TrashIcon, 
  EyeIcon,
  TagIcon,
  CurrencyDollarIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { useAuth } from '../contexts/AuthContext';
import { useBooking } from '../contexts/BookingContext';
import type { Favorite } from '../types/booking';
import DashboardCard from '../components/dashboard/DashboardCard';
import Button from '../components/ui/Button';
import SEOHead from '../components/seo/SEOHead';

const FavoritesPage: React.FC = () => {
  const { user } = useAuth();
  const { getUserFavorites, removeFromFavorites, isLoading } = useBooking();
  const [selectedType, setSelectedType] = useState<Favorite['type'] | 'all'>('all');
  const [removeLoading, setRemoveLoading] = useState<string | null>(null);

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-moon-navy mb-4">Access Denied</h2>
          <p className="text-moon-gray">Please log in to view your favorites.</p>
        </div>
      </div>
    );
  }

  const allFavorites = getUserFavorites(user.id);
  const filteredFavorites = selectedType === 'all' 
    ? allFavorites 
    : allFavorites.filter(fav => fav.type === selectedType);

  const handleRemoveFavorite = async (favoriteId: string) => {
    setRemoveLoading(favoriteId);
    try {
      await removeFromFavorites(favoriteId);
    } finally {
      setRemoveLoading(null);
    }
  };

  const getTypeIcon = (type: Favorite['type']) => {
    switch (type) {
      case 'service':
        return <TagIcon className="w-5 h-5" />;
      case 'package':
        return <CurrencyDollarIcon className="w-5 h-5" />;
      case 'gallery_image':
        return <PhotoIcon className="w-5 h-5" />;
      default:
        return <HeartIcon className="w-5 h-5" />;
    }
  };

  const getTypeColor = (type: Favorite['type']) => {
    switch (type) {
      case 'service':
        return 'bg-blue-100 text-blue-800';
      case 'package':
        return 'bg-green-100 text-green-800';
      case 'gallery_image':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const typeStats = {
    all: allFavorites.length,
    service: allFavorites.filter(f => f.type === 'service').length,
    package: allFavorites.filter(f => f.type === 'package').length,
    gallery_image: allFavorites.filter(f => f.type === 'gallery_image').length
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-moon-navy/5 to-moon-silver/10 py-8">
      <SEOHead
        title="My Favorites - Moon Event Center"
        description="View and manage your favorite services, packages, and gallery images."
        keywords={['favorites', 'saved items', 'services', 'packages', 'Moon Event Center']}
        url="https://mooneventcenter.com/favorites"
        noIndex={true}
      />

      <div className="container-max">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-serif font-bold text-white mb-2">My Favorites</h1>
            <p className="text-white/80">Your saved services, packages, and gallery images</p>
          </div>

          {/* Filter Tabs */}
          <div className="mb-8">
            <div className="border-b border-moon-silver/20">
              <nav className="-mb-px flex space-x-8">
                {[
                  { key: 'all', label: 'All Items', count: typeStats.all },
                  { key: 'service', label: 'Services', count: typeStats.service },
                  { key: 'package', label: 'Packages', count: typeStats.package },
                  { key: 'gallery_image', label: 'Gallery', count: typeStats.gallery_image }
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setSelectedType(tab.key as any)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      selectedType === tab.key
                        ? 'border-moon-gold text-moon-gold'
                        : 'border-transparent text-moon-gray hover:text-moon-navy hover:border-moon-silver'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Favorites Grid */}
          {isLoading ? (
            <DashboardCard title="Loading Favorites..." loading={true}>
              <div></div>
            </DashboardCard>
          ) : filteredFavorites.length === 0 ? (
            <DashboardCard title="No Favorites Found">
              <div className="text-center py-12">
                <HeartIcon className="w-16 h-16 text-moon-silver mx-auto mb-4" />
                <h3 className="text-lg font-medium text-moon-navy mb-2">
                  {selectedType === 'all' ? 'No favorites yet' : `No ${selectedType} favorites`}
                </h3>
                <p className="text-moon-gray mb-6">
                  Start exploring our services, packages, and gallery to save your favorites!
                </p>
                <div className="flex justify-center space-x-4">
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/services'}
                  >
                    Browse Services
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() => window.location.href = '/gallery'}
                  >
                    View Gallery
                  </Button>
                </div>
              </div>
            </DashboardCard>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredFavorites.map((favorite, index) => (
                <motion.div
                  key={favorite.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white rounded-lg shadow-md border border-moon-silver/20 overflow-hidden hover:shadow-lg transition-shadow"
                >
                  {/* Image */}
                  {favorite.itemData.imageUrl && (
                    <div className="aspect-video bg-moon-silver/10 relative overflow-hidden">
                      <img
                        src={favorite.itemData.imageUrl}
                        alt={favorite.itemData.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                      <div className="absolute top-3 right-3">
                        <HeartSolidIcon className="w-6 h-6 text-red-500" />
                      </div>
                    </div>
                  )}

                  {/* Content */}
                  <div className="p-6">
                    {/* Type Badge */}
                    <div className="flex items-center justify-between mb-3">
                      <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getTypeColor(favorite.type)}`}>
                        {getTypeIcon(favorite.type)}
                        <span className="ml-1 capitalize">{favorite.type.replace('_', ' ')}</span>
                      </span>
                      <span className="text-xs text-moon-gray">
                        {new Date(favorite.createdAt).toLocaleDateString()}
                      </span>
                    </div>

                    {/* Title */}
                    <h3 className="text-lg font-semibold text-moon-navy mb-2">
                      {favorite.itemData.title}
                    </h3>

                    {/* Description */}
                    {favorite.itemData.description && (
                      <p className="text-sm text-moon-gray mb-4 line-clamp-2">
                        {favorite.itemData.description}
                      </p>
                    )}

                    {/* Price */}
                    {favorite.itemData.price && (
                      <div className="mb-4">
                        <span className="text-lg font-bold text-moon-gold">
                          ${favorite.itemData.price.toLocaleString()}
                        </span>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center justify-between">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center space-x-1"
                      >
                        <EyeIcon className="w-4 h-4" />
                        <span>View</span>
                      </Button>

                      <button
                        onClick={() => handleRemoveFavorite(favorite.id)}
                        disabled={removeLoading === favorite.id}
                        className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
                        title="Remove from favorites"
                      >
                        {removeLoading === favorite.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                        ) : (
                          <TrashIcon className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Add to Favorites Tip */}
          {allFavorites.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-8 bg-moon-navy border border-moon-navy rounded-lg p-6"
            >
              <div className="flex items-start space-x-3">
                <HeartIcon className="w-6 h-6 text-white flex-shrink-0 mt-1" />
                <div>
                  <h3 className="font-semibold text-white mb-2">How to Add Favorites</h3>
                  <p className="text-white text-sm mb-3">
                    Look for the heart icon (♡) on services, packages, and gallery images throughout our website.
                    Click it to save items to your favorites for easy access later.
                  </p>
                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="!text-white !border-white hover:!bg-white hover:!text-moon-navy"
                      onClick={() => window.location.href = '/services'}
                    >
                      Browse Services
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="!text-white !border-white hover:!bg-white hover:!text-moon-navy"
                      onClick={() => window.location.href = '/gallery'}
                    >
                      View Gallery
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default FavoritesPage;
