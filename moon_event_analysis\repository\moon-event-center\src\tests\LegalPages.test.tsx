import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi } from 'vitest';
import TermsOfServicePage from '../pages/TermsOfServicePage';
import PrivacyPolicyPage from '../pages/PrivacyPolicyPage';

// Mock react-helmet-async to avoid React 19 compatibility issues
vi.mock('react-helmet-async', () => ({
  HelmetProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  Helmet: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Legal Pages', () => {
  describe('Terms of Service Page', () => {
    it('renders the terms of service page correctly', () => {
      render(
        <TestWrapper>
          <TermsOfServicePage />
        </TestWrapper>
      );

      // Check for main heading
      expect(screen.getByRole('heading', { name: /terms of service/i })).toBeInTheDocument();
      
      // Check for disclaimer notice
      expect(screen.getByText(/these are generic terms of service for demonstration purposes only/i)).toBeInTheDocument();
      
      // Check for key sections
      expect(screen.getByText(/acceptance of terms/i)).toBeInTheDocument();
      expect(screen.getByText(/event booking and reservations/i)).toBeInTheDocument();
      expect(screen.getByText(/payment terms/i)).toBeInTheDocument();
      expect(screen.getByText(/cancellation policy/i)).toBeInTheDocument();
      expect(screen.getByText(/liability and insurance/i)).toBeInTheDocument();
      
      // Check for contact information (use getAllByText since "Moon Event Center" appears multiple times)
      expect(screen.getAllByText(/moon event center/i)).toHaveLength(3); // Appears in multiple places
      expect(screen.getByText(/1801 n plano rd ste 200/i)).toBeInTheDocument();
      expect(screen.getByText(/\(972\) 505-8888/i)).toBeInTheDocument();
    });

    it('has proper accessibility structure', () => {
      render(
        <TestWrapper>
          <TermsOfServicePage />
        </TestWrapper>
      );

      // Check for proper heading hierarchy
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent(/terms of service/i);
      
      // Check for section headings
      const sectionHeadings = screen.getAllByRole('heading', { level: 2 });
      expect(sectionHeadings.length).toBeGreaterThan(0);
    });

    it('includes contact us link', () => {
      render(
        <TestWrapper>
          <TermsOfServicePage />
        </TestWrapper>
      );

      const contactLink = screen.getByRole('link', { name: /contact us for questions/i });
      expect(contactLink).toBeInTheDocument();
      expect(contactLink).toHaveAttribute('href', '/contact');
    });
  });

  describe('Privacy Policy Page', () => {
    it('renders the privacy policy page correctly', () => {
      render(
        <TestWrapper>
          <PrivacyPolicyPage />
        </TestWrapper>
      );

      // Check for main heading
      expect(screen.getByRole('heading', { name: /privacy policy/i })).toBeInTheDocument();
      
      // Check for disclaimer notice
      expect(screen.getByText(/this is a generic privacy policy for demonstration purposes only/i)).toBeInTheDocument();
      
      // Check for key sections (use getByRole to target the heading specifically)
      expect(screen.getByRole('heading', { name: /information we collect/i })).toBeInTheDocument();
      expect(screen.getByText(/how we use your information/i)).toBeInTheDocument();
      expect(screen.getByText(/information sharing/i)).toBeInTheDocument();
      expect(screen.getByText(/data security/i)).toBeInTheDocument();
      expect(screen.getByText(/your rights/i)).toBeInTheDocument();
      
      // Check for contact information
      expect(screen.getByText(/moon event center/i)).toBeInTheDocument();
      expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
    });

    it('has proper accessibility structure', () => {
      render(
        <TestWrapper>
          <PrivacyPolicyPage />
        </TestWrapper>
      );

      // Check for proper heading hierarchy
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent(/privacy policy/i);
      
      // Check for section headings
      const sectionHeadings = screen.getAllByRole('heading', { level: 2 });
      expect(sectionHeadings.length).toBeGreaterThan(0);
    });

    it('includes contact us link', () => {
      render(
        <TestWrapper>
          <PrivacyPolicyPage />
        </TestWrapper>
      );

      const contactLink = screen.getByRole('link', { name: /contact us for questions/i });
      expect(contactLink).toBeInTheDocument();
      expect(contactLink).toHaveAttribute('href', '/contact');
    });
  });

  describe('SEO and Meta Tags', () => {
    it('sets proper meta tags for terms of service', () => {
      render(
        <TestWrapper>
          <TermsOfServicePage />
        </TestWrapper>
      );

      // The SEOHead component should set the document title
      // Note: In a real test environment, you might need to check document.title
      // or use a more sophisticated testing approach for helmet
    });

    it('sets proper meta tags for privacy policy', () => {
      render(
        <TestWrapper>
          <PrivacyPolicyPage />
        </TestWrapper>
      );

      // The SEOHead component should set the document title
      // Note: In a real test environment, you might need to check document.title
      // or use a more sophisticated testing approach for helmet
    });
  });
});
