import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CalendarIcon, 
  ClockIcon, 
  UserGroupIcon,
  CurrencyDollarIcon,
  EyeIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { useBooking } from '../contexts/BookingContext';
import type { Booking, BookingFilter } from '../types/booking';
import DashboardCard from '../components/dashboard/DashboardCard';
import StatsCard from '../components/dashboard/StatsCard';
import DataTable, { type TableColumn } from '../components/dashboard/DataTable';
import StatusBadge from '../components/dashboard/StatusBadge';
import Button from '../components/ui/Button';
import SEOHead from '../components/seo/SEOHead';

const BookingsPage: React.FC = () => {
  const { user } = useAuth();
  const { getUserBookings, getBookingStats, isLoading } = useBooking();
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [filter, setFilter] = useState<BookingFilter>({});

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-moon-navy mb-4">Access Denied</h2>
          <p className="text-moon-gray">Please log in to view your bookings.</p>
        </div>
      </div>
    );
  }

  const bookings = getUserBookings(user.id, filter);
  const stats = getBookingStats(user.id);

  const columns: TableColumn<Booking>[] = [
    {
      key: 'eventDetails.title',
      label: 'Event',
      sortable: true,
      render: (booking) => (
        <div>
          <div className="font-medium text-moon-navy">{booking.eventDetails.title}</div>
          <div className="text-sm text-moon-gray capitalize">{booking.eventType}</div>
        </div>
      )
    },
    {
      key: 'eventDate',
      label: 'Date & Time',
      sortable: true,
      render: (booking) => (
        <div>
          <div className="font-medium text-moon-navy">
            {new Date(booking.eventDate).toLocaleDateString()}
          </div>
          <div className="text-sm text-moon-gray">{booking.eventTime}</div>
        </div>
      )
    },
    {
      key: 'guestCount',
      label: 'Guests',
      sortable: true,
      render: (booking) => (
        <div className="flex items-center space-x-1">
          <UserGroupIcon className="w-4 h-4 text-moon-gray" />
          <span>{booking.guestCount}</span>
        </div>
      )
    },
    {
      key: 'packageType',
      label: 'Package',
      sortable: true,
      render: (booking) => (
        <span className="capitalize font-medium text-moon-navy">
          {booking.packageType}
        </span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (booking) => (
        <StatusBadge status={booking.status} variant="booking" />
      )
    },
    {
      key: 'paymentStatus',
      label: 'Payment',
      sortable: true,
      render: (booking) => (
        <div>
          <StatusBadge status={booking.paymentStatus} variant="payment" size="sm" />
          <div className="text-xs text-moon-gray mt-1">
            ${booking.paidAmount.toLocaleString()} / ${booking.totalAmount.toLocaleString()}
          </div>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (booking) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setSelectedBooking(booking)}
          className="flex items-center space-x-1"
        >
          <EyeIcon className="w-4 h-4" />
          <span>View</span>
        </Button>
      )
    }
  ];

  const handleFilterChange = (newFilter: Partial<BookingFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-moon-navy/5 to-moon-silver/10 py-8">
      <SEOHead
        title="My Bookings - Moon Event Center"
        description="View and manage your event bookings at Moon Event Center."
        keywords={['bookings', 'events', 'appointments', 'Moon Event Center']}
        url="https://mooneventcenter.com/bookings"
        noIndex={true}
      />

      <div className="container-max">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-serif font-bold text-moon-navy mb-2">My Bookings</h1>
              <p className="text-moon-gray">Manage your event bookings and appointments</p>
            </div>
            <Button
              variant="primary"
              className="flex items-center space-x-2"
              onClick={() => window.location.href = '/contact'}
            >
              <PlusIcon className="w-5 h-5" />
              <span>New Booking</span>
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatsCard
              title="Total Bookings"
              value={stats.total}
              icon={CalendarIcon}
              color="blue"
              loading={isLoading}
            />
            <StatsCard
              title="Confirmed"
              value={stats.confirmed}
              icon={ClockIcon}
              color="green"
              loading={isLoading}
            />
            <StatsCard
              title="Total Spent"
              value={`$${stats.totalRevenue.toLocaleString()}`}
              icon={CurrencyDollarIcon}
              color="purple"
              loading={isLoading}
            />
            <StatsCard
              title="Pending Payment"
              value={`$${stats.pendingPayments.toLocaleString()}`}
              icon={CurrencyDollarIcon}
              color="yellow"
              loading={isLoading}
            />
          </div>

          {/* Filters */}
          <DashboardCard title="Filter Bookings" className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-moon-gray mb-2">
                  Status
                </label>
                <select
                  className="w-full px-3 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                  onChange={(e) => handleFilterChange({ 
                    status: e.target.value ? [e.target.value as Booking['status']] : undefined 
                  })}
                >
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-moon-gray mb-2">
                  Event Type
                </label>
                <select
                  className="w-full px-3 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                  onChange={(e) => handleFilterChange({ 
                    eventType: e.target.value ? [e.target.value as Booking['eventType']] : undefined 
                  })}
                >
                  <option value="">All Types</option>
                  <option value="wedding">Wedding</option>
                  <option value="corporate">Corporate</option>
                  <option value="birthday">Birthday</option>
                  <option value="anniversary">Anniversary</option>
                  <option value="quinceañera">Quinceañera</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-moon-gray mb-2">
                  Payment Status
                </label>
                <select
                  className="w-full px-3 py-2 border border-moon-silver/40 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent"
                  onChange={(e) => handleFilterChange({ 
                    paymentStatus: e.target.value ? [e.target.value as Booking['paymentStatus']] : undefined 
                  })}
                >
                  <option value="">All Payment Status</option>
                  <option value="pending">Pending</option>
                  <option value="partial">Partial</option>
                  <option value="paid">Paid</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => setFilter({})}
                  className="w-full"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </DashboardCard>

          {/* Bookings Table */}
          <DashboardCard title={`Bookings (${bookings.length})`}>
            <DataTable
              data={bookings}
              columns={columns}
              loading={isLoading}
              emptyMessage="No bookings found. Ready to plan your first event?"
              onRowClick={(booking) => setSelectedBooking(booking)}
            />
          </DashboardCard>
        </motion.div>
      </div>

      {/* Booking Details Modal */}
      {selectedBooking && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-moon-navy">
                  {selectedBooking.eventDetails.title}
                </h3>
                <button
                  onClick={() => setSelectedBooking(null)}
                  className="text-moon-gray hover:text-moon-navy"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-moon-navy mb-3">Event Details</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-moon-gray">Type:</span> <span className="capitalize">{selectedBooking.eventType}</span></div>
                    <div><span className="text-moon-gray">Date:</span> {new Date(selectedBooking.eventDate).toLocaleDateString()}</div>
                    <div><span className="text-moon-gray">Time:</span> {selectedBooking.eventTime}</div>
                    <div><span className="text-moon-gray">Guests:</span> {selectedBooking.guestCount}</div>
                    <div><span className="text-moon-gray">Package:</span> <span className="capitalize">{selectedBooking.packageType}</span></div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-moon-navy mb-3">Payment Information</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-moon-gray">Total Amount:</span> ${selectedBooking.totalAmount.toLocaleString()}</div>
                    <div><span className="text-moon-gray">Paid Amount:</span> ${selectedBooking.paidAmount.toLocaleString()}</div>
                    <div><span className="text-moon-gray">Remaining:</span> ${(selectedBooking.totalAmount - selectedBooking.paidAmount).toLocaleString()}</div>
                    <div><span className="text-moon-gray">Status:</span> <StatusBadge status={selectedBooking.paymentStatus} variant="payment" size="sm" /></div>
                  </div>
                </div>
              </div>

              {selectedBooking.eventDetails.description && (
                <div className="mt-6">
                  <h4 className="font-semibold text-moon-navy mb-3">Description</h4>
                  <p className="text-sm text-moon-gray">{selectedBooking.eventDetails.description}</p>
                </div>
              )}

              {selectedBooking.eventDetails.specialRequests && (
                <div className="mt-6">
                  <h4 className="font-semibold text-moon-navy mb-3">Special Requests</h4>
                  <p className="text-sm text-moon-gray">{selectedBooking.eventDetails.specialRequests}</p>
                </div>
              )}

              <div className="mt-8 flex justify-end space-x-4">
                <Button
                  variant="outline"
                  onClick={() => setSelectedBooking(null)}
                >
                  Close
                </Button>
                {selectedBooking.paymentStatus !== 'paid' && (
                  <Button variant="primary">
                    Make Payment
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default BookingsPage;
