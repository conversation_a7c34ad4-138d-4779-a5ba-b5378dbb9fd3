import React, { useState, useEffect } from 'react';

interface BreakpointInfo {
  name: string;
  minWidth: number;
  maxWidth?: number;
  description: string;
}

const ResponsivenessTest: React.FC = () => {
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });
  const [isVisible, setIsVisible] = useState(false);

  const breakpoints: BreakpointInfo[] = [
    { name: 'Mobile (XS)', minWidth: 0, maxWidth: 479, description: 'Small phones' },
    { name: 'Mobile (SM)', minWidth: 480, maxWidth: 639, description: 'Large phones' },
    { name: 'Tablet (MD)', minWidth: 640, maxWidth: 767, description: 'Small tablets' },
    { name: 'Tablet (LG)', minWidth: 768, maxWidth: 1023, description: 'Large tablets' },
    { name: 'Desktop (XL)', minWidth: 1024, maxWidth: 1279, description: 'Small desktops' },
    { name: 'Desktop (2XL)', minWidth: 1280, description: 'Large desktops' }
  ];

  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') return;

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    // Set initial size
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const getCurrentBreakpoint = () => {
    return breakpoints.find(bp => {
      if (bp.maxWidth) {
        return windowSize.width >= bp.minWidth && windowSize.width <= bp.maxWidth;
      }
      return windowSize.width >= bp.minWidth;
    }) || breakpoints[0];
  };

  const currentBreakpoint = getCurrentBreakpoint();

  const getBreakpointColor = (breakpoint: BreakpointInfo) => {
    if (breakpoint.name === currentBreakpoint.name) {
      return 'bg-green-100 text-green-800 border-green-300';
    }
    return 'bg-gray-100 text-gray-600 border-gray-300';
  };

  const checkResponsiveIssues = () => {
    const issues: string[] = [];

    // Check if content is too wide for mobile
    if (windowSize.width < 640) {
      const wideElements = document.querySelectorAll('*');
      wideElements.forEach(element => {
        const rect = element.getBoundingClientRect();
        if (rect.width > windowSize.width + 10) { // 10px tolerance
          issues.push(`Element wider than viewport: ${element.tagName.toLowerCase()}`);
        }
      });
    }

    // Check for horizontal scrolling
    if (document.body.scrollWidth > windowSize.width) {
      issues.push('Horizontal scrolling detected');
    }

    // Check for touch targets that are too small
    if (windowSize.width < 768) {
      const touchTargets = document.querySelectorAll('button, a, input, [role="button"]');
      touchTargets.forEach((target, index) => {
        const rect = target.getBoundingClientRect();
        if (rect.width < 44 || rect.height < 44) {
          issues.push(`Touch target ${index + 1} too small: ${rect.width}x${rect.height}px (minimum 44x44px)`);
        }
      });
    }

    return issues;
  };

  const issues = checkResponsiveIssues();

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors text-sm"
        aria-label={`Responsive test: ${windowSize.width}x${windowSize.height}`}
      >
        📱 {windowSize.width}x{windowSize.height}
      </button>

      {isVisible && (
        <div className="absolute bottom-12 left-0 w-80 bg-white border border-gray-200 rounded-lg shadow-xl">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900">Responsive Test</h3>
            <p className="text-sm text-gray-600">
              Current: {windowSize.width} × {windowSize.height}px
            </p>
          </div>
          
          <div className="p-4">
            <h4 className="font-medium text-gray-900 mb-3">Breakpoints</h4>
            <div className="space-y-2 mb-4">
              {breakpoints.map((bp) => (
                <div
                  key={bp.name}
                  className={`p-2 rounded border text-xs ${getBreakpointColor(bp)}`}
                >
                  <div className="font-medium">{bp.name}</div>
                  <div className="text-xs opacity-75">
                    {bp.maxWidth ? `${bp.minWidth}-${bp.maxWidth}px` : `${bp.minWidth}px+`}
                  </div>
                  <div className="text-xs opacity-75">{bp.description}</div>
                </div>
              ))}
            </div>

            {issues.length > 0 && (
              <div className="border-t border-gray-200 pt-4">
                <h4 className="font-medium text-red-600 mb-2">Issues Found ({issues.length})</h4>
                <div className="space-y-1">
                  {issues.slice(0, 5).map((issue, index) => (
                    <div key={index} className="text-xs text-red-600 bg-red-50 p-2 rounded">
                      {issue}
                    </div>
                  ))}
                  {issues.length > 5 && (
                    <div className="text-xs text-gray-500">
                      +{issues.length - 5} more issues...
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="border-t border-gray-200 pt-4 mt-4">
              <h4 className="font-medium text-gray-900 mb-2">Quick Tests</h4>
              <div className="space-y-2 text-xs">
                <button
                  onClick={() => {
                    window.resizeTo(375, 667); // iPhone SE
                  }}
                  className="block w-full text-left p-2 bg-gray-50 rounded hover:bg-gray-100"
                >
                  📱 iPhone SE (375×667)
                </button>
                <button
                  onClick={() => {
                    window.resizeTo(768, 1024); // iPad
                  }}
                  className="block w-full text-left p-2 bg-gray-50 rounded hover:bg-gray-100"
                >
                  📱 iPad (768×1024)
                </button>
                <button
                  onClick={() => {
                    window.resizeTo(1920, 1080); // Desktop
                  }}
                  className="block w-full text-left p-2 bg-gray-50 rounded hover:bg-gray-100"
                >
                  🖥️ Desktop (1920×1080)
                </button>
              </div>
            </div>
          </div>
          
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <p className="text-xs text-gray-600">
              This tool helps test responsive design in development mode.
              Use browser dev tools for more comprehensive testing.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResponsivenessTest;
