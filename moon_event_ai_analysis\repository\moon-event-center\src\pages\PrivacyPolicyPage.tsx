import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEOHead from '../components/seo/SEOHead';

const PrivacyPolicyPage: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  return (
    <>
      <SEOHead
        title="Privacy Policy - Moon Event Center"
        description="Privacy Policy for Moon Event Center. Learn how we collect, use, and protect your personal information."
        keywords={["privacy policy", "data protection", "Moon Event Center", "personal information", "Richardson TX"]}
      />

      <div className="min-h-screen bg-moon-white">
        {/* Hero Section */}
        <section className="section-padding bg-gradient-to-br from-moon-navy via-moon-navy to-moon-blue text-moon-white">
          <div className="container-max">
            <motion.div
              className="text-center max-w-4xl mx-auto"
              {...fadeInUp}
            >
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Privacy Policy
              </h1>
              <p className="text-xl text-moon-silver mb-8">
                Your privacy is important to us. Learn how we collect, use, and protect your information.
              </p>
              <div className="bg-red-600/20 border border-red-400 rounded-lg p-4 mb-8">
                <p className="text-red-200 font-semibold">
                  ⚠️ NOTICE: This is a generic privacy policy for demonstration purposes only. 
                  It is NOT legally binding and should be replaced with an actual policy drafted by a qualified attorney.
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Privacy Policy Content */}
        <section className="section-padding">
          <div className="container-max">
            <motion.div
              className="max-w-4xl mx-auto prose prose-lg"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">1. Information We Collect</h2>
                <p className="text-moon-charcoal mb-4">
                  We collect information you provide directly to us, such as when you create an account, 
                  make a booking, or contact us for support.
                </p>
                <h3 className="text-xl font-semibold text-moon-navy mb-2">Personal Information:</h3>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2 mb-4">
                  <li>Name, email address, and phone number</li>
                  <li>Billing and payment information</li>
                  <li>Event details and preferences</li>
                  <li>Communication preferences</li>
                </ul>
                <h3 className="text-xl font-semibold text-moon-navy mb-2">Automatically Collected Information:</h3>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>IP address and device information</li>
                  <li>Browser type and version</li>
                  <li>Pages visited and time spent on our site</li>
                  <li>Referral sources</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">2. How We Use Your Information</h2>
                <p className="text-moon-charcoal mb-4">
                  We use the information we collect to provide, maintain, and improve our services:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>Process and manage your event bookings</li>
                  <li>Communicate with you about your events and our services</li>
                  <li>Send you marketing communications (with your consent)</li>
                  <li>Improve our website and services</li>
                  <li>Comply with legal obligations</li>
                  <li>Prevent fraud and ensure security</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">3. Information Sharing</h2>
                <p className="text-moon-charcoal mb-4">
                  We do not sell, trade, or otherwise transfer your personal information to third parties, 
                  except in the following circumstances:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>With your explicit consent</li>
                  <li>To trusted service providers who assist in our operations</li>
                  <li>When required by law or to protect our rights</li>
                  <li>In connection with a business transfer or merger</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">4. Data Security</h2>
                <p className="text-moon-charcoal mb-4">
                  We implement appropriate security measures to protect your personal information:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>SSL encryption for data transmission</li>
                  <li>Secure servers and databases</li>
                  <li>Regular security audits and updates</li>
                  <li>Limited access to personal information</li>
                  <li>Employee training on data protection</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">5. Cookies and Tracking</h2>
                <p className="text-moon-charcoal mb-4">
                  We use cookies and similar technologies to enhance your experience:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>Essential cookies for website functionality</li>
                  <li>Analytics cookies to understand site usage</li>
                  <li>Marketing cookies for personalized content</li>
                  <li>You can control cookie preferences in your browser</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">6. Your Rights</h2>
                <p className="text-moon-charcoal mb-4">
                  You have certain rights regarding your personal information:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>Access and review your personal information</li>
                  <li>Correct inaccurate or incomplete information</li>
                  <li>Delete your personal information (subject to legal requirements)</li>
                  <li>Opt-out of marketing communications</li>
                  <li>Request data portability</li>
                  <li>Object to certain processing activities</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">7. Data Retention</h2>
                <p className="text-moon-charcoal mb-4">
                  We retain your personal information only as long as necessary to fulfill the purposes 
                  outlined in this policy, comply with legal obligations, resolve disputes, and enforce 
                  our agreements. Event-related information may be retained for up to 7 years for 
                  business and legal purposes.
                </p>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">8. Children's Privacy</h2>
                <p className="text-moon-charcoal mb-4">
                  Our services are not directed to children under 13. We do not knowingly collect 
                  personal information from children under 13. If we become aware that we have 
                  collected such information, we will take steps to delete it promptly.
                </p>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">9. Changes to This Policy</h2>
                <p className="text-moon-charcoal mb-4">
                  We may update this privacy policy from time to time. We will notify you of any 
                  material changes by posting the new policy on our website and updating the 
                  "Last Updated" date below.
                </p>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">10. Contact Us</h2>
                <p className="text-moon-charcoal mb-4">
                  If you have questions about this privacy policy or our data practices, please contact us:
                </p>
                <div className="text-moon-charcoal">
                  <p><strong>Moon Event Center</strong></p>
                  <p>1801 N Plano Rd Ste 200</p>
                  <p>Richardson, TX 75081</p>
                  <p>Phone: (*************</p>
                  <p>Email: <EMAIL></p>
                </div>
              </div>

              <div className="text-center mt-12">
                <p className="text-moon-silver mb-6">
                  Last updated: {new Date().toLocaleDateString()}
                </p>
                <Link
                  to="/contact"
                  className="btn-primary inline-block"
                >
                  Contact Us for Questions
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default PrivacyPolicyPage;
