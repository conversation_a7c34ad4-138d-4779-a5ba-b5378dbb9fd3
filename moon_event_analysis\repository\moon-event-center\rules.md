# Moon Event Center Website - Development Rules & Structure

## Project Overview
This document outlines the development rules, structure, and guidelines for the Moon Event Center website project.

## Tech Stack
- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: TailwindCSS 3 with custom Moon theme
- **Animations**: Framer Motion
- **UI Components**: Headless UI + Heroicons
- **Routing**: React Router DOM

## Design System Rules

### Color Palette
- **Primary Navy**: `#1a1f3a` (moon-navy)
- **Silver**: `#c0c5d0` (moon-silver) 
- **Soft White**: `#fafbfc` (moon-white)
- **Accent Gold**: `#d4af37` (moon-gold)

### Typography
- **Headers**: Playfair Display (serif)
- **Body Text**: Inter (sans-serif)
- **Font Weights**: 300, 400, 500, 600, 700

### Component Standards
- Use custom CSS classes: `.btn-primary`, `.btn-secondary`, `.btn-outline`
- Apply consistent spacing: `.section-padding`, `.container-max`
- Maintain responsive design: mobile-first approach

## File Structure Rules

```
src/
├── components/
│   ├── layout/          # Header, Footer, Navigation
│   ├── ui/              # Reusable UI components
│   ├── sections/        # Page sections (Hero, Gallery, etc.)
│   └── forms/           # Contact forms, booking forms
├── pages/               # Route components
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
└── assets/              # Static assets
```

## Development Guidelines

### Component Rules
1. All components must be TypeScript functional components
2. Use proper TypeScript interfaces for props
3. Implement proper error boundaries
4. Follow React best practices (hooks, state management)
5. Ensure accessibility (WCAG 2.1 compliance)

### Styling Rules
1. Use TailwindCSS classes primarily
2. Custom CSS only when necessary
3. Maintain consistent spacing and typography
4. Ensure mobile-first responsive design
5. Use semantic HTML elements

### Performance Rules
1. Optimize images and videos
2. Implement lazy loading for heavy content
3. Use React.memo for expensive components
4. Minimize bundle size
5. Implement proper SEO meta tags

## Page Requirements

### Homepage
- Hero video section with Moon-drone2.mp4
- Clear CTAs ("Book a Tour", "Check Availability")
- Featured events showcase
- Smooth scroll navigation

### About Us
- Moon Event Center story
- Mission statement
- Location information (Richardson, TX)

### Gallery
- Event-type categorized photos
- Responsive image grid
- Lightbox functionality

### Services/Packages
- Detailed package breakdowns
- Amenities listing
- Pricing information

### Contact
- Contact form
- Embedded Google Map
- Business information
- Parking details

## Accessibility Requirements
- WCAG 2.1 AA compliance
- Proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## SEO Requirements
- Semantic HTML structure
- Meta tags and descriptions
- Schema.org structured data
- Open Graph tags
- Fast loading speeds (<3s)

## Testing Requirements
- Component unit tests
- Integration tests
- Accessibility testing
- Cross-browser compatibility
- Mobile responsiveness testing

## Deployment Rules
- Build optimization
- Asset compression
- CDN integration
- Performance monitoring
- Error tracking

## Code Quality Standards
- ESLint configuration
- Prettier formatting
- TypeScript strict mode
- Git commit conventions
- Code review requirements
