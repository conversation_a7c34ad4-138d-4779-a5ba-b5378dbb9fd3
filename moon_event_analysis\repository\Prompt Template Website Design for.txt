
Prompt Template: Website Design for "The Moon Event Center" (Richardson, TX)

Goal:Design a stunning, modern, and responsive website for "The Moon Event Center" located in Richardson, Texas. The site should reflect elegance, professionalism, and a sense of celebration. It must be optimized for both desktop and mobile, and should appeal to clients booking weddings, corporate events, parties, and community gatherings.

1. Project Overview:

Design a website for the Moon Event Center that:

Attracts clients seeking upscale yet versatile event space

Reflects a celestial/moonlit theme subtly through design, color, and layout

Is easy to navigate, visually engaging, and conversion-oriented

2. Target Audience:

Brides and grooms

Large Parties

Corporate event planners

Superbowl  parties , sporting events, ufc  fights, television gameday

Families planning parties (quinceañeras, birthdays, anniversaries)

Nonprofits/community orgs

3. Design Style:

Visual Tone: Elegant, Mystical, SophisticatedColors: Deep navy, silver, soft white accents (moonlight-inspired)Fonts: Modern serif for headers, clean sans-serif for bodyImagery: Night skies, soft lighting, elegant table setups, dance floorsAnimations: Smooth scroll, subtle transitions, maybe a parallax moon effect

4. Page Structure:

Homepage – Hero image/video of event center, CTA, intro blurb, featured events

About Us – The story behind the Moon Event Center, mission, location

Gallery – High-quality photos categorized by event type

Services / Packages – Detailed breakdown of packages, amenities

Calendar / Availability – Option to view open dates or request booking

Contact Us – Map, form, phone/email, embedded Google Map

Testimonials – Carousel or grid of client reviews

Customer Portal - View scheduled dates, make a payment, message staff, etc. must include login system

Staff Portal - add/remove images, update descriptions, message clients, etc. must include login system

FAQ – Answers to common booking/event questions

5. Functionality / Tech Requirements:

Mobile-first responsive layout

Booking request form with calendar integration

SEO-optimized structure and metadata

Accessibility (WCAG 2.1 compliant)

Fast loading speeds

CMS for easy updates (WordPress, Webflow, or custom?)

6. Inspiration / Moodboard Notes:

(Each team member can drop links to sites with elements they like – layout, fonts, colors, transitions, etc.)

7. Must-Haves:

Clear CTA on every page ("Book a Tour", "Check Availability")

Event-type specific galleries

Social media integration (Instagram feed, facebook feed,and possibly TikTok snippets)

Embedded map and parking info

Flexible backend for staff to update pricing/images

8. Collaboration + Workflow Notes:

Event Center Layout:

Rooms:
Large area for the event
    
Bridal room: with couches, showers/bathroom etc for getting ready

9. Detailed Tech Stack & Tooling:

Frontend

React 18 with Vite for lightning‑fast HMR and production bundles.

TailwindCSS 3 (JIT) + custom "Moon" design‑token layer.

Headless UI / Radix UI components & Framer Motion for silky animations.

Vue 3 embedded via Vue Custom Elements for any micro‑components the team fancies.

Storybook for an isolated component workbench and design‑system docs.

Quality gates: TypeScript, ESLint, Prettier, Husky pre‑commit hooks.

Backend (API)

Python 3.12 + FastAPI (async, auto‑docs).

Uvicorn behind Gunicorn for prod ASGI serving.

PostgreSQL 16 (Docker) with SQLModel ORM & Alembic migrations.

Google Calendar API (via google-api-python-client) for live availability sync.

JWT auth (PyJWT) or Auth0 integration; rate‑limiting with SlowAPI.

Background jobs/email: Celery + Redis.

Testing & Quality

Playwright E2E suite covering desktop, tablet (iPad Pro), and mobile (iPhone 15) viewports; CI‑integrated video/trace uploads.

pytest for unit/integration; pytest‑cov for coverage, mutmut for mutation testing.

msw (Mock Service Worker) to stub API calls in Storybook & Playwright.

Faker / Factory Boy for rich test fixtures.

DevOps / CI‑CD

Docker Compose for local dev; multi‑stage Dockerfile for prod.

GitHub Actions: lint → type‑check → unit tests → Playwright → build & push images → deploy.

Deployment targets: Cpanel, AWS Lightsail, or DigitalOcean App Platform.

Observability: OpenTelemetry tracing piped to Grafana Tempo & Loki, Sentry for error monitoring.

Accessibility & SEO

Automated axe‑core scans in Playwright.

Meta & structured‑data via react‑helmet + schema.org JSON‑LD helpers.

About Us Story: The Moon Event Center is a unique upscale venue nelocated in the heart of Richardson, Texas. Our mission is to provide couples and event planners with a breathtaking space that combines technology with Functionality.  With state of the art audio and visual technology, The Moon offers a complete package allowing for an array of users such as corporate powerpoint presentations, private movie theater / game rooms and one can only imaging hosting private superbowl or sporting event parties. we have the expertise and amenities to make your event unforgettable

Augment Code Specific:

Create a rules.md file with the program structure and rules for the website.

Create a living checklist and integrate it's use with the rules file

use https://pier59studios.com/events?utm_term=event%20venues%20nyc&utm_campaign=Piranha+%7C+Non+Branded+%7C+Events+%7C+Search&utm_source=adwords&utm_medium=ppc&hsa_acc=4735745939&hsa_cam=22692615730&hsa_grp=180526534745&hsa_ad=759029764491&hsa_src=g&hsa_tgt=kwd-2166216027061&hsa_kw=event%20venues%20nyc&hsa_mt=e&hsa_net=adwords&hsa_ver=3&gad_source=1&gad_campaignid=22692615730&gbraid=0AAAAApd8vEzUfgiS6aiisIRHVb_ydrQyr&gclid=Cj0KCQjw953DBhCyARIsANhIZoam_P89rO1IAZYUuGkSu_AOG9AHI4d3pHKNtcuzWTR0AnOdkzSzPy0aArPCEALw_wcB as inspiration for the website design.

I have added Moon-drone2.mp4 to the assets folder for the drone video of the event center.  This is to play on repeat at the top of the page like in the example page.

You have multiple MCP servers available to you.  Please use them to their fullest potential.  This includes servers for vue and playwright compontents and the Magic MCP as well.

Use the Context7 MCP to ensure you have the latest updates per the documentation.

