import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import LoginForm from '../components/auth/LoginForm';
import RegisterForm from '../components/auth/RegisterForm';
import SEOHead from '../components/seo/SEOHead';

type AuthMode = 'login' | 'register' | 'forgot-password';

const AuthPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [authMode, setAuthMode] = useState<AuthMode>('login');

  // Get the intended destination from location state
  const from = (location.state as any)?.from?.pathname || '/';

  const handleAuthSuccess = () => {
    // Redirect to the intended page or home
    navigate(from, { replace: true });
  };

  const handleForgotPassword = () => {
    setAuthMode('forgot-password');
  };

  const renderAuthForm = () => {
    switch (authMode) {
      case 'login':
        return (
          <LoginForm
            onSuccess={handleAuthSuccess}
            onSwitchToRegister={() => setAuthMode('register')}
            onForgotPassword={handleForgotPassword}
          />
        );
      case 'register':
        return (
          <RegisterForm
            onSuccess={handleAuthSuccess}
            onSwitchToLogin={() => setAuthMode('login')}
          />
        );
      case 'forgot-password':
        return (
          <ForgotPasswordForm
            onBackToLogin={() => setAuthMode('login')}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-moon-navy/5 to-moon-silver/10">
      <SEOHead
        title={authMode === 'login' ? 'Sign In - Moon Event Center' : 'Create Account - Moon Event Center'}
        description={authMode === 'login' 
          ? 'Sign in to your Moon Event Center account to manage bookings and access exclusive features.'
          : 'Create your Moon Event Center account to book events and access our customer portal.'
        }
        keywords={[
          'Moon Event Center login',
          'customer portal',
          'event booking account',
          'wedding venue login',
          'Richardson event center account'
        ]}
        url={`https://mooneventcenter.com/auth`}
        type="website"
        noIndex={true} // Don't index auth pages
      />

      <div className="container-max py-20">
        <div className="flex items-center justify-center min-h-[calc(100vh-10rem)]">
          <AnimatePresence mode="wait">
            <motion.div
              key={authMode}
              initial={{ opacity: 0, x: authMode === 'register' ? 50 : -50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: authMode === 'register' ? -50 : 50 }}
              transition={{ duration: 0.3 }}
              className="w-full"
            >
              {renderAuthForm()}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

// Forgot Password Form Component
interface ForgotPasswordFormProps {
  onBackToLogin: () => void;
}

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({ onBackToLogin }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    if (!email) {
      setError('Please enter your email address');
      setIsLoading(false);
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setIsSuccess(true);
    } catch (error) {
      setError('Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md mx-auto"
      >
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-serif font-bold text-moon-navy mb-4">
            Check Your Email
          </h2>
          <p className="text-moon-gray mb-6">
            We've sent a password reset link to <strong>{email}</strong>
          </p>
          <button
            onClick={onBackToLogin}
            className="text-moon-gold hover:text-moon-gold/80 font-medium transition-colors"
          >
            Back to Sign In
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-md mx-auto"
    >
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-serif font-bold text-moon-navy mb-2">
            Reset Password
          </h2>
          <p className="text-moon-gray">
            Enter your email to receive a reset link
          </p>
        </div>

        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6"
          >
            {error}
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="reset-email" className="block text-sm font-medium text-moon-navy mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="reset-email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-4 py-3 border border-moon-silver/30 rounded-lg focus:ring-2 focus:ring-moon-gold focus:border-transparent transition-colors"
              placeholder="Enter your email"
              required
              disabled={isLoading}
            />
          </div>

          <button
            type="submit"
            className="w-full bg-moon-gold text-white py-3 px-4 rounded-lg hover:bg-moon-gold/90 transition-colors font-medium disabled:opacity-50"
            disabled={isLoading}
          >
            {isLoading ? 'Sending...' : 'Send Reset Link'}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={onBackToLogin}
            className="text-moon-gold hover:text-moon-gold/80 font-medium transition-colors"
            disabled={isLoading}
          >
            Back to Sign In
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default AuthPage;
