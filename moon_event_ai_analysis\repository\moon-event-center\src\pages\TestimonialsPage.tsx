import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import TestimonialsSection from '../components/sections/TestimonialsSection';
import SEOHead from '../components/seo/SEOHead';

const TestimonialsPage: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const stats = [
    { number: '500+', label: 'Happy Couples', description: 'Weddings celebrated' },
    { number: '1000+', label: 'Events Hosted', description: 'Successful celebrations' },
    { number: '98%', label: 'Client Satisfaction', description: 'Would recommend us' },
    { number: '15+', label: 'Years Experience', description: 'In event planning' }
  ];

  return (
    <div className="min-h-screen">
      <SEOHead
        title="Client Testimonials - Moon Event Center Reviews"
        description="Read real testimonials from couples and event planners who chose Moon Event Center for their weddings and special events in Richardson, Texas."
        keywords={[
          'Moon Event Center reviews',
          'wedding venue testimonials',
          'Richardson TX event reviews',
          'wedding venue feedback',
          'event center testimonials',
          'client reviews Richardson',
          'wedding venue ratings'
        ]}
        url="https://mooneventcenter.com/testimonials"
        type="website"
      />

      {/* Hero Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-moon-navy mb-6">
              Client Testimonials
            </h1>
            <p className="text-xl md:text-2xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
              Discover why couples and event planners choose Moon Event Center for their most important celebrations
            </p>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-moon-gold mb-2">
                  {stat.number}
                </div>
                <div className="text-lg font-semibold text-moon-navy mb-1">
                  {stat.label}
                </div>
                <div className="text-sm text-moon-navy">
                  {stat.description}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Main Testimonials Section - Grid View */}
      <TestimonialsSection
        title="Real Stories from Real Celebrations"
        subtitle="Read what our clients have to say about their experience with Moon Event Center"
        displayMode="grid"
        showControls={false}
        autoPlay={false}
        maxItems={6}
      />

      {/* Featured Testimonial Carousel */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <TestimonialsSection
            title="Featured Reviews"
            subtitle="Highlighted experiences from our recent events"
            displayMode="carousel"
            showControls={true}
            autoPlay={true}
            maxItems={3}
            className="!py-0"
          />
        </div>
      </section>

      {/* Review Categories */}
      <section className="section-padding">
        <div className="container-max">
          <motion.div
            variants={{
              animate: {
                transition: {
                  staggerChildren: 0.1
                }
              }
            }}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <motion.h2 
              variants={fadeInUp}
              className="text-3xl md:text-4xl font-serif font-bold text-moon-white mb-6"
            >
              Reviews by Event Type
            </motion.h2>
            <motion.p 
              variants={fadeInUp}
              className="text-xl text-moon-white max-w-3xl mx-auto leading-relaxed"
            >
              See what clients say about their specific type of celebration
            </motion.p>
          </motion.div>

          <motion.div
            variants={{
              animate: {
                transition: {
                  staggerChildren: 0.1
                }
              }
            }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                type: 'Weddings',
                count: '200+',
                description: 'Dream weddings brought to life',
                icon: '💒',
                color: 'bg-pink-50 border-pink-200'
              },
              {
                type: 'Corporate Events',
                count: '150+',
                description: 'Professional gatherings and galas',
                icon: '🏢',
                color: 'bg-blue-50 border-blue-200'
              },
              {
                type: 'Quinceañeras',
                count: '80+',
                description: 'Beautiful coming-of-age celebrations',
                icon: '👑',
                color: 'bg-purple-50 border-purple-200'
              },
              {
                type: 'Anniversaries',
                count: '60+',
                description: 'Milestone celebrations of love',
                icon: '💕',
                color: 'bg-red-50 border-red-200'
              },
              {
                type: 'Birthday Parties',
                count: '100+',
                description: 'Memorable birthday celebrations',
                icon: '🎂',
                color: 'bg-yellow-50 border-yellow-200'
              },
              {
                type: 'Special Events',
                count: '75+',
                description: 'Graduations, reunions, and more',
                icon: '🎉',
                color: 'bg-green-50 border-green-200'
              }
            ].map((category, index) => (
              <motion.div
                key={category.type}
                variants={fadeInUp}
                className={`p-6 rounded-lg border-2 ${category.color} text-center hover:shadow-lg transition-shadow`}
              >
                <div className="text-4xl mb-4">{category.icon}</div>
                <h3 className="text-xl font-semibold text-moon-navy mb-2">{category.type}</h3>
                <div className="text-2xl font-bold text-moon-gold mb-2">{category.count}</div>
                <p className="text-moon-navy text-sm">{category.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
              Ready to Create Your Own Success Story?
            </h2>
            <p className="text-xl text-moon-navy mb-8 max-w-2xl mx-auto">
              Join hundreds of satisfied clients who have celebrated their most important moments with us.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact" className="btn-secondary">
                Schedule Your Tour
              </Link>
              <Link to="/services" className="btn-outline border-moon-white text-moon-white hover:bg-moon-white hover:text-moon-navy">
                View Our Packages
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default TestimonialsPage;
