import React from 'react';

interface StatusBadgeProps {
  status: string;
  variant?: 'default' | 'booking' | 'payment';
  size?: 'sm' | 'md' | 'lg';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  variant = 'default',
  size = 'md' 
}) => {
  const getStatusStyles = () => {
    const baseClasses = 'inline-flex items-center font-medium rounded-full';
    
    const sizeClasses = {
      sm: 'px-2 py-1 text-xs',
      md: 'px-2.5 py-1.5 text-sm',
      lg: 'px-3 py-2 text-base'
    };

    let colorClasses = '';

    if (variant === 'booking') {
      switch (status.toLowerCase()) {
        case 'pending':
          colorClasses = 'bg-yellow-100 text-yellow-800 border border-yellow-200';
          break;
        case 'confirmed':
          colorClasses = 'bg-green-100 text-green-800 border border-green-200';
          break;
        case 'cancelled':
          colorClasses = 'bg-red-100 text-red-800 border border-red-200';
          break;
        case 'completed':
          colorClasses = 'bg-blue-100 text-blue-800 border border-blue-200';
          break;
        default:
          colorClasses = 'bg-gray-100 text-gray-800 border border-gray-200';
      }
    } else if (variant === 'payment') {
      switch (status.toLowerCase()) {
        case 'pending':
          colorClasses = 'bg-yellow-100 text-yellow-800 border border-yellow-200';
          break;
        case 'partial':
          colorClasses = 'bg-orange-100 text-orange-800 border border-orange-200';
          break;
        case 'paid':
          colorClasses = 'bg-green-100 text-green-800 border border-green-200';
          break;
        case 'refunded':
          colorClasses = 'bg-purple-100 text-purple-800 border border-purple-200';
          break;
        default:
          colorClasses = 'bg-gray-100 text-gray-800 border border-gray-200';
      }
    } else {
      // Default variant
      switch (status.toLowerCase()) {
        case 'active':
        case 'success':
        case 'completed':
        case 'approved':
          colorClasses = 'bg-green-100 text-green-800 border border-green-200';
          break;
        case 'pending':
        case 'waiting':
        case 'in progress':
          colorClasses = 'bg-yellow-100 text-yellow-800 border border-yellow-200';
          break;
        case 'cancelled':
        case 'rejected':
        case 'failed':
        case 'error':
          colorClasses = 'bg-red-100 text-red-800 border border-red-200';
          break;
        case 'draft':
        case 'inactive':
          colorClasses = 'bg-gray-100 text-gray-800 border border-gray-200';
          break;
        default:
          colorClasses = 'bg-moon-silver/20 text-moon-navy border border-moon-silver/40';
      }
    }

    return `${baseClasses} ${sizeClasses[size]} ${colorClasses}`;
  };

  const formatStatus = (status: string) => {
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <span className={getStatusStyles()}>
      {formatStatus(status)}
    </span>
  );
};

export default StatusBadge;
