import React, { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') return;

    const measurePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      const newMetrics: Partial<PerformanceMetrics> = {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      };

      // First Contentful Paint
      const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
      if (fcp) {
        newMetrics.firstContentfulPaint = fcp.startTime;
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            setMetrics(prev => ({
              ...prev,
              largestContentfulPaint: lastEntry.startTime
            }));
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            setMetrics(prev => ({
              ...prev,
              cumulativeLayoutShift: clsValue
            }));
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              setMetrics(prev => ({
                ...prev,
                firstInputDelay: (entry as any).processingStart - entry.startTime
              }));
            }
          });
          fidObserver.observe({ entryTypes: ['first-input'] });
        } catch (error) {
          console.warn('Performance Observer not fully supported:', error);
        }
      }

      setMetrics(newMetrics);
    };

    // Wait for page to load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
      return () => window.removeEventListener('load', measurePerformance);
    }
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const getScoreColor = (value: number, thresholds: { good: number; needs: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.needs) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTime = (time: number) => {
    if (time < 1000) return `${Math.round(time)}ms`;
    return `${(time / 1000).toFixed(2)}s`;
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-700 transition-colors text-sm"
        aria-label="Performance metrics"
      >
        ⚡ Perf
      </button>

      {isVisible && (
        <div className="absolute bottom-12 right-0 w-80 bg-white border border-gray-200 rounded-lg shadow-xl">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900">Performance Metrics</h3>
            <p className="text-sm text-gray-600">Core Web Vitals & Load Times</p>
          </div>
          
          <div className="p-4 space-y-3">
            {/* Load Time */}
            {metrics.loadTime !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">Load Time</span>
                <span className={`text-sm font-mono ${getScoreColor(metrics.loadTime, { good: 1000, needs: 3000 })}`}>
                  {formatTime(metrics.loadTime)}
                </span>
              </div>
            )}

            {/* DOM Content Loaded */}
            {metrics.domContentLoaded !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">DOM Ready</span>
                <span className={`text-sm font-mono ${getScoreColor(metrics.domContentLoaded, { good: 800, needs: 1600 })}`}>
                  {formatTime(metrics.domContentLoaded)}
                </span>
              </div>
            )}

            {/* First Contentful Paint */}
            {metrics.firstContentfulPaint !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">FCP</span>
                <span className={`text-sm font-mono ${getScoreColor(metrics.firstContentfulPaint, { good: 1800, needs: 3000 })}`}>
                  {formatTime(metrics.firstContentfulPaint)}
                </span>
              </div>
            )}

            {/* Largest Contentful Paint */}
            {metrics.largestContentfulPaint !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">LCP</span>
                <span className={`text-sm font-mono ${getScoreColor(metrics.largestContentfulPaint, { good: 2500, needs: 4000 })}`}>
                  {formatTime(metrics.largestContentfulPaint)}
                </span>
              </div>
            )}

            {/* Cumulative Layout Shift */}
            {metrics.cumulativeLayoutShift !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">CLS</span>
                <span className={`text-sm font-mono ${getScoreColor(metrics.cumulativeLayoutShift, { good: 0.1, needs: 0.25 })}`}>
                  {metrics.cumulativeLayoutShift.toFixed(3)}
                </span>
              </div>
            )}

            {/* First Input Delay */}
            {metrics.firstInputDelay !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">FID</span>
                <span className={`text-sm font-mono ${getScoreColor(metrics.firstInputDelay, { good: 100, needs: 300 })}`}>
                  {formatTime(metrics.firstInputDelay)}
                </span>
              </div>
            )}
          </div>

          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="text-xs text-gray-600 space-y-1">
              <div><span className="text-green-600">●</span> Good</div>
              <div><span className="text-yellow-600">●</span> Needs Improvement</div>
              <div><span className="text-red-600">●</span> Poor</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
